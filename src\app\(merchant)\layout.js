import { SidebarProvider } from "@/context/SidebarContext";
import { ToastContainer } from "react-toastify";

export const metadata = {
  title: "Merchant Dashboard | Money Chain",
  description: "Generated by create next app",
};

export default function MerchantLayout({ children }) {
  return (
    <div
    >
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnHover
        draggable
        theme="light"
      />
      <SidebarProvider>{children}</SidebarProvider>
    </div>
  );
}
