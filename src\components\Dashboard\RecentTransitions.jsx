"use client";
import { <PERSON>, TableBody, Table<PERSON>ell, TableHeader, TableRow } from "../ui";
import Image from "next/image";
import Badge from "../ui/badge/Badge";
import NetworkService from "@/network/service/network_service";
import ApiPath from "@/network/api/api_path";
import { useEffect, useState } from "react";
import tableDesImg from "../../../public/assets/user/product-01.webp";

function RecentTransitions() {
  // use network
  const networkService = new NetworkService();
  const [transitionData, setTransitionData] = useState([]);

  // Fetch transition data
  const fetchTransitionData = async () => {
    try {
      const res = await networkService.get(ApiPath.recentTransition);
      setTransitionData(res.data.data.transactions);
    } finally {
    }
  };

  useEffect(() => {
    fetchTransitionData();
  }, []);

  return (
    <>
      <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
        <div className="max-w-full overflow-x-auto">
          <div className="min-w-[1102px]">
            <Table>
              {/* Table Header */}
              <TableHeader className="border-b border-gray-100 dark:border-white/[0.05]">
                <TableRow>
                  <TableCell
                    isHeader
                    className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                  >
                    Description
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                  >
                    Transaction ID
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                  >
                    Type
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                  >
                    Amount
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                  >
                    Charge
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                  >
                    Status
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                  >
                    Method
                  </TableCell>
                </TableRow>
              </TableHeader>

              {/* Table Body */}
              <TableBody className="divide-y divide-gray-100 dark:divide-white/[0.05]">
                {transitionData.map((item) => (
                  <TableRow key={item.tnx}>
                    <TableCell className="px-5 py-4 sm:px-6 text-start">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 overflow-hidden rounded-full">
                          <Image
                            width={40}
                            height={40}
                            src={tableDesImg}
                            alt={tableDesImg}
                          />
                        </div>
                        <div>
                          <span className="block font-medium text-gray-800 text-theme-sm dark:text-white/90">
                            {item?.description}
                          </span>
                          <span className="block text-gray-500 text-theme-xs dark:text-gray-400">
                            {item?.created_at}
                          </span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                      {item?.tnx}
                    </TableCell>
                    <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                      {item?.type}
                    </TableCell>
                    <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                      <span
                        className={
                          item.is_plus ? "text-green-700" : "text-red-700"
                        }
                      >
                        {item.is_plus ? "+" : "-"}
                        {item.amount}
                        <span className="ml-1 text-xs">
                          {item.is_plus ? "↑" : "↓"}
                        </span>
                      </span>
                    </TableCell>
                    <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                      {item?.charge}
                    </TableCell>
                    <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                      <Badge
                        size="sm"
                        variant="solid"
                        color={
                          item?.status === "Success"
                            ? "success"
                            : item?.status === "Pending"
                            ? "warning"
                            : "error"
                        }
                      >
                        {item?.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                      {item?.method}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>
    </>
  );
}

export default RecentTransitions;
